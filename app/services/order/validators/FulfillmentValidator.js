/**
 * Fulfillment Validator
 *
 * This service validates orders and line items for fulfillment requirements,
 * ensuring only appropriate items are processed for invoicing.
 */

import { container } from '../../../lib/container/ServiceContainer.server.js';
import { ValidationError } from '../../../lib/errors/AppError.js';

/**
 * Validator for fulfillment requirements
 */
export class FulfillmentValidator {
  constructor(dependencies = {}) {
    this.prisma = dependencies.prisma;
    this.initialized = false;

    if (!this.prisma) {
      this.initializeAsync();
    } else {
      this.initialized = true;
    }
  }

  async initializeAsync() {
    if (!this.initialized) {
      this.prisma = await container.resolve('database');
      this.initialized = true;
    }
  }

  async ensureInitialized() {
    if (!this.initialized) {
      await this.initializeAsync();
    }
  }

  /**
   * Validate an entire order for processing
   * @param {object} orderData - Shopify order data
   * @param {string} shopDomain - Shop domain
   * @returns {Promise<object>} - Validation result
   */
  async validateOrder(orderData, shopDomain) {
    const validationResult = {
      isValid: true,
      errors: [],
      warnings: [],
      lineItemResults: [],
    };

    try {
      // Validate order-level requirements
      this.validateOrderLevel(orderData, validationResult);

      // Validate each line item
      for (const lineItem of orderData.line_items || []) {
        const lineItemResult = await this.validateLineItem(lineItem, orderData, shopDomain);
        validationResult.lineItemResults.push(lineItemResult);

        if (!lineItemResult.isValid) {
          validationResult.errors.push(...lineItemResult.errors);
        }

        if (lineItemResult.warnings.length > 0) {
          validationResult.warnings.push(...lineItemResult.warnings);
        }
      }

      // Check if any line items are valid for processing
      const validLineItems = validationResult.lineItemResults.filter(r => r.isValid);
      const skippedLineItems = validationResult.lineItemResults.filter(r =>
        !r.isValid && r.warnings.some(w =>
          w.includes('skipped for shop') || w.includes('fulfillment service skipped for shop')
        )
      );

      // If all items are skipped (not errors), then the order should be skipped, not failed
      if (validLineItems.length === 0) {
        if (skippedLineItems.length === validationResult.lineItemResults.length) {
          // All items are skipped - this is not an error, just skip the order
          validationResult.isValid = true;
          validationResult.warnings.push('All line items skipped for this shop');
        } else {
          // Some items have actual errors
          validationResult.isValid = false;
          validationResult.errors.push('No line items are valid for processing');
        }
      }

      return validationResult;

    } catch (error) {
      validationResult.isValid = false;
      validationResult.errors.push(`Validation error: ${error.message}`);
      return validationResult;
    }
  }

  /**
   * Validate order-level requirements
   * @param {object} orderData - Shopify order data
   * @param {object} validationResult - Validation result to update
   */
  validateOrderLevel(orderData, validationResult) {
    // Note: All orders should be processed regardless of financial status

    // Check if order is cancelled
    if (orderData.cancelled_at) {
      validationResult.errors.push('Order is cancelled');
      validationResult.isValid = false;
    }

    // Check if order has line items
    if (!orderData.line_items || orderData.line_items.length === 0) {
      validationResult.errors.push('Order has no line items');
      validationResult.isValid = false;
    }

    // Check order total
    const totalPrice = parseFloat(orderData.total_price || 0);
    if (totalPrice <= 0) {
      validationResult.warnings.push('Order has zero or negative total');
    }

    // Check order age
    const createdAt = new Date(orderData.created_at);
    const daysSinceCreated = (Date.now() - createdAt.getTime()) / (1000 * 60 * 60 * 24);

    if (daysSinceCreated > 365) {
      validationResult.warnings.push('Order is more than 1 year old');
    }
  }

  /**
   * Validate a single line item
   * @param {object} lineItem - Shopify line item
   * @param {object} orderData - Complete order data
   * @param {string} shopDomain - Shop domain
   * @returns {Promise<object>} - Line item validation result
   */
  async validateLineItem(lineItem, orderData, shopDomain) {
    const result = {
      lineItemId: lineItem.id,
      sku: lineItem.sku || '',
      isValid: true,
      errors: [],
      warnings: [],
      fulfillmentInfo: null,
    };

    try {
      // Validate basic line item data
      this.validateBasicLineItemData(lineItem, result);

      // Validate fulfillment service
      const fulfillmentInfo = await this.validateFulfillmentService(lineItem, shopDomain);
      result.fulfillmentInfo = fulfillmentInfo;

      if (!fulfillmentInfo.isValid) {
        result.isValid = false;
        result.errors.push(...fulfillmentInfo.errors);
      }

      // Validate quantity and pricing
      this.validateQuantityAndPricing(lineItem, result);

      // Validate product-specific requirements
      await this.validateProductSpecificRequirements(lineItem, shopDomain, result);

      return result;

    } catch (error) {
      result.isValid = false;
      result.errors.push(`Line item validation error: ${error.message}`);
      return result;
    }
  }

  /**
   * Validate basic line item data
   * @param {object} lineItem - Shopify line item
   * @param {object} result - Validation result to update
   */
  validateBasicLineItemData(lineItem, result) {
    // Check required fields
    if (!lineItem.id) {
      result.errors.push('Line item missing ID');
      result.isValid = false;
    }

    if (!lineItem.product_id) {
      result.errors.push('Line item missing product ID');
      result.isValid = false;
    }

    if (!lineItem.variant_id) {
      result.errors.push('Line item missing variant ID');
      result.isValid = false;
    }

    // Check for gift cards
    if (lineItem.gift_card) {
      result.errors.push('Gift cards are not processed');
      result.isValid = false;
    }

    // Check if line item requires shipping
    if (lineItem.requires_shipping === false) {
      result.warnings.push('Line item does not require shipping');
    }
  }

  /**
   * Validate fulfillment service
   * @param {object} lineItem - Shopify line item
   * @param {string} shopDomain - Shop domain
   * @returns {Promise<object>} - Fulfillment validation result
   */
  async validateFulfillmentService(lineItem, shopDomain) {
    const result = {
      isValid: true,
      errors: [],
      warnings: [],
      service: lineItem.fulfillment_service || 'manual',
      isManual: false,
      dbRecord: null,
    };

    try {
      // Get fulfillment service from database
      const dbRecord = await this.prisma.variantFulfillmentService.findFirst({
        where: {
          variantId: String(lineItem.variant_id),
        },
        orderBy: {
          lastUpdated: 'desc',
        },
      });

      result.dbRecord = dbRecord;

      // Determine actual fulfillment service
      const actualService = dbRecord?.fulfillmentService || lineItem.fulfillment_service || 'manual';
      result.service = actualService;
      result.isManual = actualService === 'manual';

      // Validate fulfillment service
      if (!result.isManual) {
        // Treat non-manual fulfillment services as skipped items, not errors
        result.isValid = false;
        result.warnings.push(`Non-manual fulfillment service skipped for shop: ${actualService}`);
      }

      // Check for inconsistencies
      if (dbRecord && dbRecord.fulfillmentService !== lineItem.fulfillment_service) {
        result.warnings.push(
          `Fulfillment service mismatch: DB=${dbRecord.fulfillmentService}, Shopify=${lineItem.fulfillment_service}`
        );
      }

      return result;

    } catch (error) {
      result.warnings.push(`Failed to check fulfillment service: ${error.message}`);
      return result;
    }
  }

  /**
   * Validate quantity and pricing
   * @param {object} lineItem - Shopify line item
   * @param {object} result - Validation result to update
   */
  validateQuantityAndPricing(lineItem, result) {
    // Validate quantity
    const quantity = lineItem.quantity;
    if (typeof quantity !== 'number' || quantity <= 0) {
      result.errors.push('Invalid quantity');
      result.isValid = false;
    } else if (quantity > 1000) {
      result.warnings.push(`High quantity: ${quantity}`);
    }

    // Price validation removed - price comes from separate source
  }

  /**
   * Validate product-specific requirements
   * @param {object} lineItem - Shopify line item
   * @param {string} shopDomain - Shop domain
   * @param {object} result - Validation result to update
   */
  async validateProductSpecificRequirements(lineItem, shopDomain, result) {
    const sku = lineItem.sku || '';

    // Validate SKU format
    if (!sku) {
      result.warnings.push('Line item has no SKU');
    } else if (sku.length > 100) {
      result.warnings.push('SKU is very long');
    }

    // Check for special SKU patterns
    if (sku.startsWith('*F')) {
      // Flag/patch product validation
      await this.validateFlagPatchProduct(lineItem, shopDomain, result);
    } else if (sku.toLowerCase().includes('custom')) {
      // Custom product validation
      this.validateCustomProduct(lineItem, result);
    }

    // Validate vendor
    if (lineItem.vendor && lineItem.vendor.length > 100) {
      result.warnings.push('Vendor name is very long');
    }

    // Validate product title
    if (!lineItem.title) {
      result.warnings.push('Line item has no title');
    } else if (lineItem.title.length > 500) {
      result.warnings.push('Product title is very long');
    }
  }

  /**
   * Validate flag/patch products
   * @param {object} lineItem - Shopify line item
   * @param {string} shopDomain - Shop domain
   * @param {object} result - Validation result to update
   */
  async validateFlagPatchProduct(lineItem, shopDomain, result) {
    const includedShops = [
      'american-trigger-pullers.myshopify.com',
      'phaselineco.myshopify.com',
      'phaselineco-fulfillment.myshopify.com',
    ];

    if (!includedShops.includes(shopDomain)) {
      // Mark as invalid but don't add to errors - this will cause the item to be skipped
      result.isValid = false;
      result.warnings.push(`Flag/patch products skipped for shop: ${shopDomain}`);
    } else {
      result.warnings.push('Flag/patch product - special processing rules apply');
    }
  }

  /**
   * Validate custom products
   * @param {object} lineItem - Shopify line item
   * @param {object} result - Validation result to update
   */
  validateCustomProduct(lineItem, result) {
    const properties = lineItem.properties || [];

    if (properties.length === 0) {
      result.warnings.push('Custom product has no customization properties');
    }

    // Check for required customization fields
    const hasCustomText = properties.some(p =>
      p.name?.toLowerCase().includes('text') ||
      p.name?.toLowerCase().includes('message')
    );

    const hasCustomImage = properties.some(p =>
      p.name?.toLowerCase().includes('image') ||
      p.name?.toLowerCase().includes('logo')
    );

    if (!hasCustomText && !hasCustomImage) {
      result.warnings.push('Custom product has no text or image customization');
    }
  }

  /**
   * Get validation statistics
   * @param {string} shopDomain - Shop domain
   * @param {object} dateRange - Date range filter
   * @returns {Promise<object>} - Validation statistics
   */
  async getValidationStatistics(shopDomain, dateRange = {}) {
    try {
      // This would require tracking validation results in the database
      // For now, return basic statistics
      return {
        description: 'Fulfillment validation service',
        validationRules: [
          'Financial status must be paid',
          'Order must not be cancelled',
          'Line items must have manual fulfillment service',
          'Valid quantity and pricing required',
          'Shop-specific product inclusion rules',
        ],
        supportedProductTypes: [
          'Standard products',
          'Flag/patch products (*F SKU prefix)',
          'Custom products',
        ],
      };
    } catch (error) {
      return {
        error: error.message,
      };
    }
  }
}
